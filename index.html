<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Story Generator</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="index.css" />
    <script src="https://js.puter.com/v2/"></script>
</head>
  <body>
    <div id="main-layout">
      <aside id="sidebar">
        <div class="sidebar-header">
          <button id="new-chat-btn">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            <span>New Story</span>
          </button>
        </div>
        <nav id="chat-history-container">
          <ul id="chat-history-list">
            <!-- Chat history items will be dynamically inserted here -->
          </ul>
        </nav>
        <div class="sidebar-footer">
          <button id="settings-btn" aria-label="Open Settings">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19.14 12.94c.04-.3.06-.61.06-.94s-.02-.64-.07-.94l2.03-1.58a.5.5 0 0 0 .12-.61l-1.92-3.32a.5.5 0 0 0-.61-.22l-2.49 1a9.6 9.6 0 0 0-1.64-.94l-.38-2.65a.5.5 0 0 0-.5-.43h-3.84a.5.5 0 0 0-.5.43l-.38 2.65a9.6 9.6 0 0 0-1.64.94l-2.49-1a.5.5 0 0 0-.61.22l-1.92 3.32a.5.5 0 0 0 .12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58a.5.5 0 0 0-.12.61l1.92 3.32a.5.5 0 0 0 .61.22l2.49-1a9.6 9.6 0 0 0 1.64.94l.38 2.65a.5.5 0 0 0 .5.43h3.84a.5.5 0 0 0 .5-.43l.38-2.65a9.6 9.6 0 0 0 1.64-.94l2.49 1a.5.5 0 0 0 .61-.22l1.92-3.32a.5.5 0 0 0-.12-.61l-2.03-1.58z"></path><circle cx="12" cy="12" r="3"></circle></svg>
            <span>Settings</span>
          </button>
        </div>
      </aside>
      <div id="app-container">
        <header>
          <h1>AI Story Generator</h1>
          <p>Your creative partner for imaginative tales</p>
        </header>
        <main id="chat-container"></main>
        <footer>
          <form id="prompt-form">
            <textarea
              id="prompt-input"
              placeholder="Tell me a story about..."
              aria-label="Story prompt"
              rows="1"
            ></textarea>
            <button type="submit" aria-label="Send message">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
              </svg>
            </button>
          </form>
        </footer>
      </div>
    </div>

    <div id="settings-modal" class="modal-overlay hidden">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Settings</h2>
          <button id="close-modal-btn" aria-label="Close settings">&times;</button>
        </div>
        <form id="settings-form">
          <div class="form-group">
            <label for="model-select">AI Model</label>
            <p class="form-description">Choose the AI model for story generation. Different models have different strengths and capabilities.</p>
            <select id="model-select">
              <optgroup label="OpenAI Models">
                <option value="gpt-4o">GPT-4o (Recommended)</option>
                <option value="gpt-4.1">GPT-4.1</option>
                <option value="gpt-4.1-mini">GPT-4.1 Mini</option>
                <option value="gpt-4.1-nano">GPT-4.1 Nano</option>
                <option value="gpt-4.5-preview">GPT-4.5 Preview</option>
                <option value="gpt-4o-mini">GPT-4o Mini</option>
                <option value="o1">o1 (Not supported for chat)</option>
                <option value="o1-mini">o1 Mini (Not supported for chat)</option>
                <option value="o1-pro">o1 Pro (Not supported for chat)</option>
                <option value="o3">o3 (Not supported for chat)</option>
                <option value="o3-mini">o3 Mini (Not supported for chat)</option>
                <option value="o4-mini">o4 Mini</option>
                <option value="openrouter:openai/chatgpt-4o-latest">ChatGPT-4o Latest</option>
                <option value="openrouter:openai/gpt-4">GPT-4</option>
                <option value="openrouter:openai/gpt-4-turbo">GPT-4 Turbo</option>
                <option value="openrouter:openai/gpt-3.5-turbo-instruct">GPT-3.5 Turbo Instruct</option>
              </optgroup>
              <optgroup label="Anthropic Models">
                <option value="openrouter:anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                <option value="openrouter:anthropic/claude-3.5-haiku">Claude 3.5 Haiku</option>
                <option value="openrouter:anthropic/claude-3-opus">Claude 3 Opus</option>
                <option value="openrouter:anthropic/claude-3.7-sonnet">Claude 3.7 Sonnet</option>
                <option value="openrouter:anthropic/claude-sonnet-4">Claude Sonnet 4</option>
                <option value="openrouter:anthropic/claude-opus-4">Claude Opus 4</option>
              </optgroup>
              <optgroup label="Meta Llama Models">
                <option value="openrouter:meta-llama/llama-3.3-70b-instruct">Llama 3.3 70B</option>
                <option value="openrouter:meta-llama/llama-3.1-405b-instruct">Llama 3.1 405B</option>
                <option value="openrouter:meta-llama/llama-3.1-70b-instruct">Llama 3.1 70B</option>
                <option value="openrouter:meta-llama/llama-3.1-8b-instruct">Llama 3.1 8B</option>
                <option value="openrouter:meta-llama/llama-4-maverick">Llama 4 Maverick</option>
                <option value="openrouter:meta-llama/llama-4-scout">Llama 4 Scout</option>
              </optgroup>
              <optgroup label="Google Models">
                <option value="openrouter:google/gemini-2.5-flash">Gemini 2.5 Flash</option>
                <option value="openrouter:google/gemini-2.5-pro">Gemini 2.5 Pro</option>
                <option value="openrouter:google/gemini-2.0-flash-001">Gemini 2.0 Flash</option>
                <option value="openrouter:google/gemini-flash-1.5">Gemini Flash 1.5</option>
                <option value="openrouter:google/gemini-pro-1.5">Gemini Pro 1.5</option>
                <option value="openrouter:google/gemma-3-27b-it">Gemma 3 27B</option>
                <option value="openrouter:google/gemma-3-12b-it">Gemma 3 12B</option>
              </optgroup>
              <optgroup label="Mistral Models">
                <option value="openrouter:mistralai/mistral-large-2411">Mistral Large</option>
                <option value="openrouter:mistralai/mistral-medium-3">Mistral Medium 3</option>
                <option value="openrouter:mistralai/mistral-small">Mistral Small</option>
                <option value="openrouter:mistralai/mistral-nemo">Mistral Nemo</option>
                <option value="openrouter:mistralai/codestral-2501">Codestral</option>
                <option value="openrouter:mistralai/pixtral-large-2411">Pixtral Large</option>
              </optgroup>
              <optgroup label="DeepSeek Models">
                <option value="openrouter:deepseek/deepseek-r1">DeepSeek R1</option>
                <option value="openrouter:deepseek/deepseek-chat">DeepSeek Chat</option>
                <option value="openrouter:deepseek/deepseek-r1-distill-llama-70b">DeepSeek R1 Distill 70B</option>
                <option value="openrouter:deepseek/deepseek-r1-distill-qwen-32b">DeepSeek R1 Distill 32B</option>
              </optgroup>
              <optgroup label="Qwen Models">
                <option value="openrouter:qwen/qwen3-235b-a22b">Qwen3 235B</option>
                <option value="openrouter:qwen/qwen3-32b">Qwen3 32B</option>
                <option value="openrouter:qwen/qwen-2.5-72b-instruct">Qwen 2.5 72B</option>
                <option value="openrouter:qwen/qwq-32b">QwQ 32B</option>
              </optgroup>
              <optgroup label="X.AI Models">
                <option value="openrouter:x-ai/grok-3">Grok 3</option>
                <option value="openrouter:x-ai/grok-3-mini">Grok 3 Mini</option>
                <option value="openrouter:x-ai/grok-2-1212">Grok 2</option>
              </optgroup>
              <optgroup label="Perplexity Models">
                <option value="openrouter:perplexity/sonar-pro">Sonar Pro</option>
                <option value="openrouter:perplexity/sonar-reasoning">Sonar Reasoning</option>
                <option value="openrouter:perplexity/r1-1776">R1-1776</option>
              </optgroup>
              <optgroup label="Other Models">
                <option value="openrouter:cohere/command-r-plus">Cohere Command R+</option>
                <option value="openrouter:nvidia/llama-3.1-nemotron-70b-instruct">NVIDIA Nemotron 70B</option>
                <option value="openrouter:microsoft/phi-4">Microsoft Phi-4</option>
                <option value="openrouter:liquid/lfm-40b">Liquid LFM 40B</option>
                <option value="openrouter:minimax/minimax-01">MiniMax-01</option>
              </optgroup>
            </select>
          </div>
          <div class="form-group">
            <label for="context-size-select">Context Window</label>
            <p class="form-description">Number of past messages to send for context. "All" provides the best memory but may be slower. For very long stories, using a fixed number can help.</p>
            <select id="context-size-select">
              <option value="2">Last 2 Messages</option>
              <option value="4">Last 4 Messages</option>
              <option value="6">Last 6 Messages</option>
              <option value="10">Last 10 Messages</option>
              <option value="all">All Messages (Default)</option>
            </select>
          </div>
          <div class="form-group">
            <label>Chat Management</label>
            <p class="form-description">Permanently clear all messages from the current story. This cannot be undone.</p>
            <button type="button" id="clear-messages-btn" class="btn-danger">Clear Current Story</button>
          </div>
        </form>
      </div>
    </div>

    <script type="module" src="index.tsx"></script>
</body>
</html>
