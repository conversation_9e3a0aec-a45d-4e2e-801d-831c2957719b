:root {
  --background-color: #212121;
  --sidebar-bg: #1e1e1e;
  --app-background: #2a2a2a;
  --header-color: #ffffff;
  --text-color: #e0e0e0;
  --light-text-color: #9e9e9e;
  --user-message-bg: #00897b;
  --user-message-text: #ffffff;
  --ai-message-bg: #373737;
  --ai-message-text: #e0e0e0;
  --border-color: #424242;
  --shadow-color: rgba(0, 0, 0, 0.2);
  --hover-bg: #373737;
  --active-bg: #00796b;
  --green-accent: #00bfa5;
  --danger-color: #e53935;
  --danger-hover-color: #c62828;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  overflow: hidden;
}

#main-layout {
  display: flex;
  width: 100vw;
  height: 100vh;
}

#sidebar {
  width: 260px;
  background-color: var(--sidebar-bg);
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  border-right: 1px solid var(--border-color);
}

.sidebar-header {
  padding-bottom: 0.75rem;
}

#new-chat-btn {
  width: 100%;
  padding: 0.75rem;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  font-family: 'Inter', sans-serif;
  transition: background-color 0.2s, border-color 0.2s;
}

#new-chat-btn:hover {
  background-color: var(--hover-bg);
  border-color: var(--light-text-color);
}

#new-chat-btn svg {
  width: 18px;
  height: 18px;
}

#chat-history-container {
  flex-grow: 1;
  overflow-y: auto;
  margin-top: 0.75rem;
}

#chat-history-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.chat-history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  user-select: none;
}

.chat-history-item:hover {
  background-color: var(--hover-bg);
}

.chat-history-item.active {
  background-color: var(--active-bg);
  color: #fff;
  font-weight: 500;
}

.chat-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.delete-chat-btn {
  background: none;
  border: none;
  color: var(--light-text-color);
  cursor: pointer;
  padding: 0.25rem;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, color 0.2s;
  flex-shrink: 0;
}
.delete-chat-btn svg {
  width: 16px;
  height: 16px;
  display: block;
}

.chat-history-item:hover .delete-chat-btn {
  opacity: 1;
  visibility: visible;
}

.chat-history-item.active .delete-chat-btn,
.chat-history-item .delete-chat-btn:hover {
  color: #fff;
}

.sidebar-footer {
  margin-top: auto;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

#settings-btn {
  width: 100%;
  padding: 0.75rem;
  background: none;
  border: none;
  color: var(--light-text-color);
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  font-family: 'Inter', sans-serif;
  transition: background-color 0.2s, color 0.2s;
}

#settings-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

#settings-btn svg {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}


#app-container {
  flex-grow: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--app-background);
}

header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--app-background);
  text-align: center;
}

header h1 {
  font-size: 1.5rem;
  color: var(--header-color);
  font-weight: 700;
}

header p {
  color: var(--light-text-color);
  font-size: 0.9rem;
}

#chat-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 85%;
  animation: fadeIn 0.3s ease-in-out;
}

.message-content {
  padding: 0.75rem 1rem;
  border-radius: 12px;
  line-height: 1.5;
  font-size: 0.95rem;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.user-message {
  align-self: flex-end;
  align-items: flex-end;
}

.user-message .message-content {
  background-color: var(--user-message-bg);
  color: var(--user-message-text);
  border-bottom-right-radius: 4px;
}

.ai-message {
  align-self: flex-start;
  align-items: flex-start;
}

.ai-message .message-content {
  background-color: var(--ai-message-bg);
  color: var(--ai-message-text);
  border-bottom-left-radius: 4px;
}

footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--app-background);
}

#prompt-form {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  background-color: var(--ai-message-bg);
  transition: box-shadow 0.2s, border-color 0.2s;
}

#prompt-form:focus-within {
  border-color: var(--green-accent);
  box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.3);
}

#prompt-input {
  flex-grow: 1;
  border: none;
  background: transparent;
  font-family: 'Inter', sans-serif;
  font-size: 1rem;
  resize: none;
  height: 24px;
  line-height: 24px;
  color: var(--text-color);
  outline: none;
  padding: 0;
  max-height: 200px;
}

#prompt-input::placeholder {
  color: var(--light-text-color);
}

#prompt-form button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background-color: var(--green-accent);
  color: black;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
}

#prompt-form button:hover {
  background-color: #00a794;
}

#prompt-form button:active {
  transform: scale(0.95);
}

#prompt-form button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

#prompt-form button svg {
  width: 20px;
  height: 20px;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 4px;
}

.loading-dots div {
  width: 8px;
  height: 8px;
  background-color: var(--ai-message-text);
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots .dot1 {
  animation-delay: -0.32s;
}
.loading-dots .dot2 {
  animation-delay: -0.16s;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay:not(.hidden) {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--app-background);
  padding: 2rem;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  border: 1px solid var(--border-color);
  box-shadow: 0 5px 15px var(--shadow-color);
  transform: scale(0.95);
  transition: transform 0.3s;
}
.modal-overlay:not(.hidden) .modal-content {
    transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

.modal-header h2 {
  color: var(--header-color);
  font-size: 1.25rem;
}

#close-modal-btn {
  background: none;
  border: none;
  color: var(--light-text-color);
  font-size: 2rem;
  line-height: 1;
  cursor: pointer;
  transition: color 0.2s;
}

#close-modal-btn:hover {
  color: var(--text-color);
}

.form-group {
  margin-bottom: 1.5rem;
}
.form-group label {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}
.form-description {
  font-size: 0.85rem;
  color: var(--light-text-color);
  margin-bottom: 0.75rem;
}

#settings-form select, #settings-form button {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--ai-message-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  border-radius: 8px;
  font-size: 0.9rem;
  font-family: 'Inter', sans-serif;
  cursor: pointer;
}

#settings-form select:focus, #settings-form button:focus {
    border-color: var(--green-accent);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.3);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    font-weight: 500;
    transition: background-color 0.2s;
}
.btn-danger:hover {
    background-color: var(--danger-hover-color);
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Style for unsupported model options */
#model-select option[value="o1"],
#model-select option[value="o1-mini"],
#model-select option[value="o1-pro"],
#model-select option[value="o3"],
#model-select option[value="o3-mini"] {
  color: var(--light-text-color);
  font-style: italic;
}
