<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puter.js Test</title>
    <script src="https://js.puter.com/v2/"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        pre {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Puter.js Integration Test</h1>
        <p>This page tests the Puter.js integration to help diagnose any 404 errors.</p>
        
        <div>
            <button id="checkPuter">Check Puter.js Loading</button>
            <button id="checkAuth">Check Authentication</button>
            <button id="signIn">Sign In</button>
            <button id="testChat">Test Chat API</button>
            <button id="testImage">Test Image Generation</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(title, content, type = 'result') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        // Wait for Puter.js to load
        function waitForPuter() {
            return new Promise((resolve) => {
                if (typeof window.puter !== 'undefined') {
                    resolve(true);
                } else {
                    setTimeout(() => waitForPuter().then(resolve), 100);
                }
            });
        }
        
        document.getElementById('checkPuter').addEventListener('click', async () => {
            clearResults();
            try {
                await waitForPuter();
                addResult('Puter.js Loading Check', 
                    `✅ Puter.js loaded successfully
puter object: ${typeof window.puter}
puter.ai: ${typeof window.puter.ai}
puter.ai.chat: ${typeof window.puter.ai?.chat}
puter.ai.txt2img: ${typeof window.puter.ai?.txt2img}
puter.auth: ${typeof window.puter.auth}`, 'success');
            } catch (error) {
                addResult('Puter.js Loading Check', `❌ Error: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('checkAuth').addEventListener('click', async () => {
            clearResults();
            try {
                await waitForPuter();
                const isSignedIn = await window.puter.auth.isSignedIn();
                let userInfo = 'Not signed in';
                
                if (isSignedIn) {
                    const user = await window.puter.auth.getUser();
                    userInfo = JSON.stringify(user, null, 2);
                }
                
                addResult('Authentication Check', 
                    `Signed in: ${isSignedIn}
User info: ${userInfo}`, isSignedIn ? 'success' : 'result');
            } catch (error) {
                addResult('Authentication Check', `❌ Error: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('signIn').addEventListener('click', async () => {
            clearResults();
            try {
                await waitForPuter();
                addResult('Sign In', 'Attempting to sign in...', 'result');
                await window.puter.auth.signIn();
                const isSignedIn = await window.puter.auth.isSignedIn();
                addResult('Sign In Result', `Signed in: ${isSignedIn}`, isSignedIn ? 'success' : 'error');
            } catch (error) {
                addResult('Sign In', `❌ Error: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('testChat').addEventListener('click', async () => {
            clearResults();
            try {
                await waitForPuter();
                addResult('Chat API Test', 'Testing chat API...', 'result');
                
                const response = await window.puter.ai.chat('Hello! Please respond with just "Hello back!" to test the API.', {
                    model: 'gpt-4o'
                });
                
                addResult('Chat API Result', `✅ Success!
Response: ${response}`, 'success');
            } catch (error) {
                addResult('Chat API Test', `❌ Error: ${error.message}
Status: ${error.status || 'Unknown'}
URL: ${error.url || 'Unknown'}`, 'error');
            }
        });
        
        document.getElementById('testImage').addEventListener('click', async () => {
            clearResults();
            try {
                await waitForPuter();
                addResult('Image Generation Test', 'Testing image generation...', 'result');
                
                const imageElement = await window.puter.ai.txt2img('A simple red circle on white background');
                
                addResult('Image Generation Result', `✅ Success!
Image dimensions: ${imageElement.width}x${imageElement.height}
Image type: ${imageElement.constructor.name}`, 'success');
                
                // Display the image
                const imgDiv = document.createElement('div');
                imgDiv.className = 'result success';
                imgDiv.innerHTML = '<h3>Generated Image</h3>';
                imgDiv.appendChild(imageElement);
                resultsDiv.appendChild(imgDiv);
                
            } catch (error) {
                addResult('Image Generation Test', `❌ Error: ${error.message}
Status: ${error.status || 'Unknown'}
URL: ${error.url || 'Unknown'}`, 'error');
            }
        });
        
        // Auto-check Puter.js loading on page load
        window.addEventListener('load', () => {
            document.getElementById('checkPuter').click();
        });
    </script>
</body>
</html>
