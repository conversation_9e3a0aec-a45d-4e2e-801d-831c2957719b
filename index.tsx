// Declare puter as a global variable for TypeScript
declare global {
  interface Window {
    puter: any;
  }
}

type ChatMessage = {
  id: string;
  role: "user" | "model";
  parts: { text: string }[];
};

type ChatSession = {
  id: string;
  title: string;
  history: ChatMessage[];
};

type Settings = {
  contextMessageCount: number | 'all';
  selectedModel: string;
};

const App = () => {
  // --- UI Elements ---
  const form = document.getElementById("prompt-form") as HTMLFormElement;
  const input = document.getElementById("prompt-input") as HTMLTextAreaElement;
  const chatContainer = document.getElementById("chat-container") as HTMLElement;
  const submitButton = form.querySelector('button[type="submit"]') as HTMLButtonElement;
  const newChatBtn = document.getElementById("new-chat-btn") as HTMLButtonElement;
  const chatHistoryList = document.getElementById("chat-history-list") as HTMLUListElement;

  // --- Settings Modal Elements ---
  const settingsModal = document.getElementById("settings-modal") as HTMLDivElement;
  const openSettingsBtn = document.getElementById("settings-btn") as HTMLButtonElement;
  const closeSettingsBtn = document.getElementById("close-modal-btn") as HTMLButtonElement;
  const contextSizeSelect = document.getElementById("context-size-select") as HTMLSelectElement;
  const modelSelect = document.getElementById("model-select") as HTMLSelectElement;
  const clearMessagesBtn = document.getElementById("clear-messages-btn") as HTMLButtonElement;

  // --- State ---
  let chats: ChatSession[] = [];
  let activeChatId: string | null = null;
  let settings: Settings;

  // --- Data Persistence ---
  const saveChats = () => {
    localStorage.setItem("story-chats-v2", JSON.stringify(chats));
  };
  
  const loadChats = (): ChatSession[] => {
    const savedChats = localStorage.getItem("story-chats-v2");
    try {
      return savedChats ? JSON.parse(savedChats) : [];
    } catch {
      return [];
    }
  };
  
  const saveSettings = () => {
    localStorage.setItem('story-settings-v1', JSON.stringify(settings));
  };
  
  const loadSettings = (): Settings => {
    const savedSettings = localStorage.getItem('story-settings-v1');
    const defaultSettings: Settings = {
      contextMessageCount: 'all',
      selectedModel: 'gpt-4o'
    };
    try {
      const settings = savedSettings ? { ...defaultSettings, ...JSON.parse(savedSettings) } : defaultSettings;

      // All models are now supported with proper handling

      return settings;
    } catch {
      return defaultSettings;
    }
  };

  // --- Core Chat Logic ---

  const handleFormSubmit = async (e: Event) => {
    e.preventDefault();
    const prompt = input.value.trim();
    const currentChat = chats.find((c) => c.id === activeChatId);
    if (!prompt || !currentChat) return;

    // Check if user is signed in before making AI requests
    try {
      const isSignedIn = await window.puter.auth.isSignedIn();
      if (!isSignedIn) {
        console.log("User not signed in, prompting for authentication...");
        await window.puter.auth.signIn();
        // After sign-in, check again
        const nowSignedIn = await window.puter.auth.isSignedIn();
        if (!nowSignedIn) {
          console.log("User cancelled sign-in");
          return;
        }
        console.log("User successfully signed in");
      }
    } catch (authError) {
      console.error("Authentication error:", authError);
      const errorMessage: ChatMessage = {
        id: `msg-error-${Date.now()}`,
        role: 'model',
        parts: [{ text: `Authentication required. Please refresh the page and try again. Error: ${authError.message}` }]
      };
      currentChat.history.push(errorMessage);
      saveChats();
      displayChat(activeChatId as string);
      return;
    }

    // All models are now supported with proper o1 handling

    // If this is the first real user prompt, clear the welcome message
    if (currentChat.history.length === 1 && currentChat.history[0].id.startsWith('welcome')) {
      currentChat.history = [];
    }

    const userMessage: ChatMessage = {
      id: `msg-user-${Date.now()}`,
      role: "user",
      parts: [{ text: prompt }],
    };
    currentChat.history.push(userMessage);

    if (currentChat.history.filter(m => m.role === 'user').length === 1) {
      currentChat.title = prompt.substring(0, 40) + (prompt.length > 40 ? '...' : '');
    }

    input.value = "";
    autoResizeTextarea();
    setFormDisabled(true);
    renderChatHistory();

    displayChat(activeChatId as string);
    const loadingWrapper = showLoadingIndicator();
    const loadingContent = loadingWrapper.querySelector('.message-content') as HTMLElement;

    let fullResponse = "";
    try {
      const history = currentChat.history;
      let conversationHistory = "";
      
      // Build conversation history for context
      if (settings.contextMessageCount === 'all') {
        conversationHistory = history.slice(0, -1).map(m => 
          `${m.role === 'user' ? 'User' : 'Assistant'}: ${m.parts[0].text}`
        ).join('\n\n');
      } else if (history.length > 1) {
        const contextMessages = history.slice(Math.max(0, history.length - 1 - settings.contextMessageCount), -1);
        conversationHistory = contextMessages.map(m => 
          `${m.role === 'user' ? 'User' : 'Assistant'}: ${m.parts[0].text}`
        ).join('\n\n');
      }

      // Create the full prompt with system instruction and context
      const fullPrompt = `You are a master storyteller who writes stories chapter by chapter. Based on the user's prompt, write a single, detailed chapter of an ongoing story. It is crucial that you only write ONE chapter at a time. End the chapter at a natural stopping point or a cliffhanger, then wait for the user's next prompt to continue. Do not ask 'What happens next?'; simply end the chapter. Maintain strict consistency with the plot and characters from previous chapters. Format your response in plain text.

${conversationHistory ? `Previous conversation:\n${conversationHistory}\n\n` : ''}Current request: ${prompt}`;

      // Use streaming for real-time response
      try {
        console.log("Attempting to call puter.ai.chat with model:", settings.selectedModel);
        const stream = await window.puter.ai.chat(fullPrompt, {
          model: settings.selectedModel,
          stream: true
        });

        loadingContent.innerHTML = "";
        for await (const chunk of stream) {
          const chunkText = chunk?.text || '';
          if (chunkText) {
            loadingContent.textContent += chunkText;
            fullResponse += chunkText;
            scrollToBottom();
          }
        }
      } catch (streamError) {
        console.warn("Streaming failed, trying non-streaming:", streamError);

        // Extract error details from Puter.js error format
        const errorDetails = streamError?.error || streamError;
        console.error("Stream error details:", {
          success: streamError?.success,
          error: errorDetails,
          message: errorDetails?.message || streamError?.message,
          code: errorDetails?.code,
          type: errorDetails?.type
        });

        // Fallback to non-streaming if streaming fails
        try {
          loadingContent.innerHTML = "";
          console.log("Attempting non-streaming chat request...");
          const response = await window.puter.ai.chat(fullPrompt, {
            model: settings.selectedModel
          });
          fullResponse = response;
          loadingContent.textContent = fullResponse;
          scrollToBottom();
        } catch (nonStreamError) {
          console.error("Non-streaming also failed:", nonStreamError);

          // Extract error details from Puter.js error format
          const nonStreamErrorDetails = nonStreamError?.error || nonStreamError;
          console.error("Non-stream error details:", {
            success: nonStreamError?.success,
            error: nonStreamErrorDetails,
            message: nonStreamErrorDetails?.message || nonStreamError?.message,
            code: nonStreamErrorDetails?.code,
            type: nonStreamErrorDetails?.type
          });
          throw nonStreamError; // Re-throw to be caught by outer catch
        }
      }
      
      loadingWrapper.remove();

      if (fullResponse) {
        const modelMessage: ChatMessage = {
          id: `msg-model-${Date.now()}`,
          role: "model",
          parts: [{ text: fullResponse }],
        };
        currentChat.history.push(modelMessage);
        saveChats();
      }
      displayChat(activeChatId as string);

    } catch (error) {
      console.error("Error sending message:", error);
      console.error("Full error object:", JSON.stringify(error, null, 2));
      loadingWrapper.remove();

      // Extract meaningful error message from Puter.js error format
      let errorMessage = "An unknown error occurred.";
      if (error?.error?.message) {
        errorMessage = error.error.message;

        // Special handling for different error types
        if (errorMessage.includes("This model is only supported in v1/responses")) {
          errorMessage = `The ${settings.selectedModel} model uses a different API endpoint that is not currently supported. Please select a different model in Settings (try GPT-4o, Claude Sonnet 4, or Gemini 2.0 Flash).`;
        } else if (errorMessage.includes("Permission denied")) {
          errorMessage = `Permission denied for ${settings.selectedModel}. Please try a different model like GPT-4o, GPT-4.1, or Claude Sonnet 4.`;
        } else if (errorMessage.includes("usage-limited-chat")) {
          errorMessage = `The ${settings.selectedModel} model has usage limitations or requires special access. Please try GPT-4o, GPT-4.1, Claude Sonnet 4, or Gemini 2.0 Flash instead.`;
        }
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (error?.error?.type) {
        errorMessage = `Error type: ${error.error.type}`;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      const errorBubble: ChatMessage = {
        id: `msg-error-${Date.now()}`,
        role: 'model',
        parts: [{ text: `Sorry, something went wrong: ${errorMessage}` }]
      };
      currentChat.history.push(errorBubble);
      saveChats();
      displayChat(activeChatId as string);
    } finally {
      setFormDisabled(false);
      input.focus();
    }
  };

  // --- UI Management ---
  const renderChatHistory = () => {
    chatHistoryList.innerHTML = "";
    chats.forEach((chat) => {
      const item = document.createElement("li");
      item.classList.add("chat-history-item");
      item.dataset.chatId = chat.id;
      if (chat.id === activeChatId) {
        item.classList.add("active");
      }

      const title = document.createElement("span");
      title.classList.add("chat-title");
      title.textContent = chat.title;

      const deleteBtn = document.createElement("button");
      deleteBtn.classList.add("delete-chat-btn");
      deleteBtn.setAttribute("aria-label", `Delete chat: ${chat.title}`);
      deleteBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>`;

      item.appendChild(title);
      item.appendChild(deleteBtn);
      chatHistoryList.appendChild(item);

      item.addEventListener("click", (e) => {
        if (e.target !== deleteBtn && !deleteBtn.contains(e.target as Node)) {
          switchChat(chat.id);
        }
      });
      deleteBtn.addEventListener("click", (e) => {
        e.stopPropagation();
        deleteChat(chat.id);
      });
    });
  };

  const showLoadingIndicator = () => {
    const messageWrapper = document.createElement("div");
    messageWrapper.classList.add("message", "ai-message");
    const messageContent = document.createElement("div");
    messageContent.classList.add("message-content");
    messageContent.innerHTML = `<div class="loading-dots"><div class="dot1"></div><div class="dot2"></div><div class="dot3"></div></div>`;
    messageWrapper.appendChild(messageContent);
    chatContainer.appendChild(messageWrapper);
    scrollToBottom();
    return messageWrapper;
  };

  const displayChat = (chatId: string) => {
    const chat = chats.find((c) => c.id === chatId);
    if (!chat) return;

    activeChatId = chatId;
    chatContainer.innerHTML = "";

    chat.history.forEach((message) => {
      const messageWrapper = document.createElement("div");
      messageWrapper.classList.add("message", `${message.role}-message`);
      messageWrapper.dataset.messageId = message.id;

      const messageContent = document.createElement("div");
      messageContent.classList.add("message-content");
      if (message.id.startsWith('msg-error')) messageContent.style.color = '#ff8a80';
      messageContent.textContent = message.parts[0].text;
      messageWrapper.appendChild(messageContent);


      chatContainer.appendChild(messageWrapper);
    });

    renderChatHistory();
    setFormDisabled(false);
    scrollToBottom();
  };

  const switchChat = (chatId: string) => {
    if (chatId === activeChatId) return;
    displayChat(chatId);
  };

  const startNewChat = () => {
    const newChat: ChatSession = {
      id: `chat-${Date.now()}`,
      title: "New Story",
      history: [{
        id: `welcome-${Date.now()}`,
        role: 'model',
        parts: [{ text: "Hello! Let's write a story together, chapter by chapter. What should the first chapter be about?" }]
      }],
    };
    chats.unshift(newChat);
    saveChats();
    displayChat(newChat.id);
  };

  const deleteChat = (chatId: string) => {
    chats = chats.filter((c) => c.id !== chatId);
    saveChats();

    if (activeChatId === chatId) {
      if (chats.length > 0) {
        displayChat(chats[0].id);
      } else {
        startNewChat();
      }
    }
    renderChatHistory();
  };

  const clearCurrentChatMessages = () => {
    if (!activeChatId) return;
    const currentChat = chats.find((c) => c.id === activeChatId);
    if (currentChat && confirm('Are you sure you want to clear all messages in this story? This cannot be undone.')) {
      currentChat.history = [{
        id: `welcome-${Date.now()}`,
        role: 'model',
        parts: [{ text: "Hello! Let's write a story together, chapter by chapter. What should the first chapter be about?" }]
      }];
      saveChats();
      displayChat(activeChatId);
    }
  };

  // --- Settings Modal Logic ---
  const setupSettingsModal = () => {
    openSettingsBtn.addEventListener('click', () => settingsModal.classList.remove('hidden'));
    closeSettingsBtn.addEventListener('click', () => settingsModal.classList.add('hidden'));
    settingsModal.addEventListener('click', (e) => {
      if (e.target === settingsModal) {
        settingsModal.classList.add('hidden');
      }
    });

    // Set initial values
    contextSizeSelect.value = String(settings.contextMessageCount);
    modelSelect.value = settings.selectedModel;

    // Handle context size changes
    contextSizeSelect.addEventListener('change', () => {
      const value = contextSizeSelect.value;
      settings.contextMessageCount = value === 'all' ? 'all' : parseInt(value, 10);
      saveSettings();
    });

    // Handle model selection changes
    modelSelect.addEventListener('change', () => {
      settings.selectedModel = modelSelect.value;
      saveSettings();
    });

    clearMessagesBtn.addEventListener('click', () => {
      clearCurrentChatMessages();
      settingsModal.classList.add('hidden');
    });
  }

  // --- Helpers ---
  const setFormDisabled = (disabled: boolean) => {
    input.disabled = disabled;
    submitButton.disabled = disabled;
    submitButton.innerHTML = disabled ? '...' : `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" /></svg>`;
  };

  const autoResizeTextarea = () => {
    input.style.height = "auto";
    input.style.height = `${input.scrollHeight}px`;
  };

  const scrollToBottom = () => {
    chatContainer.scrollTop = chatContainer.scrollHeight;
  };

  // --- Initial Setup ---
  const initializeApp = async () => {
    // Wait for Puter.js to be available
    if (typeof window.puter === 'undefined') {
      console.log("Waiting for Puter.js to load...");
      setTimeout(initializeApp, 100);
      return;
    }

    console.log("Puter.js loaded successfully:", window.puter);

    // Test Puter.js API availability
    console.log("Testing Puter.js API endpoints...");
    console.log("puter.ai available:", typeof window.puter.ai);
    console.log("puter.ai.chat available:", typeof window.puter.ai?.chat);

    // Check authentication status
    console.log("Checking authentication status...");
    try {
      const isSignedIn = await window.puter.auth.isSignedIn();
      console.log("User signed in:", isSignedIn);

      if (isSignedIn) {
        const user = await window.puter.auth.getUser();
        console.log("Current user:", user);
      } else {
        console.log("User not signed in - AI features may require authentication");
        // Optionally prompt for sign-in
        // await window.puter.auth.signIn();
      }
    } catch (authError) {
      console.warn("Authentication check failed:", authError);
    }

    form.addEventListener("submit", handleFormSubmit);
    newChatBtn.addEventListener("click", startNewChat);
    input.addEventListener("input", autoResizeTextarea);
    input.addEventListener("keydown", (e) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        form.requestSubmit();
      }
    });

    settings = loadSettings();
    console.log("Settings loaded:", settings);
    setupSettingsModal();

    chats = loadChats();
    if (chats.length === 0) {
      startNewChat();
    } else {
      displayChat(chats[0].id);
    }
  };

  initializeApp();
};

App();