# AI Story Generator with Puter.js

An interactive story generator that creates stories chapter by chapter using Puter.js for AI capabilities and image generation.

## Features

- **Chapter-by-chapter storytelling**: AI writes one chapter at a time based on your prompts
- **Multiple chat sessions**: Manage multiple stories simultaneously
- **Context management**: Configurable memory settings for story consistency
- **No API keys required**: Uses Puter.js with user-pays model for AI access
- **Persistent storage**: Stories are saved locally in your browser

## Technology Stack

- **Frontend**: Vanilla TypeScript, HTML, CSS
- **AI Provider**: Puter.js (100+ AI models available)
- **Build Tool**: Vite
- **Authentication**: Handled automatically by Puter.js

## Run Locally

**Prerequisites:** Node.js

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run the development server:
   ```bash
   npm run dev
   ```

3. Open your browser to `http://localhost:5173`

## How It Works

1. **No Setup Required**: Puter.js handles all AI API access and authentication
2. **User Authentication**: Users sign in with their Puter.com account when needed
3. **User-Pays Model**: Each user covers their own AI usage costs
4. **Story Generation**: Uses AI models for creative writing with streaming responses

## Usage

1. Start a new story by clicking "New Story"
2. Enter a prompt for your first chapter
3. The AI will generate a single chapter and stop at a natural point
4. Continue the story by providing the next prompt
5. Manage multiple stories in the sidebar
6. Adjust context settings in the Settings modal

## Available AI Models

The app supports 100+ AI models through Puter.js and OpenRouter, organized by provider:

### OpenAI Models
- GPT-4o (Recommended), GPT-4.1, GPT-4.1 Mini, GPT-4.1 Nano
- GPT-4.5 Preview, GPT-4o Mini, o1, o1 Mini, o1 Pro
- o3, o3 Mini, o4 Mini, ChatGPT-4o Latest
- GPT-4, GPT-4 Turbo, GPT-3.5 Turbo Instruct

### Anthropic Models
- Claude 3.5 Sonnet, Claude 3.5 Haiku, Claude 3 Opus
- Claude 3.7 Sonnet, Claude Sonnet 4, Claude Opus 4

### Meta Llama Models
- Llama 3.3 70B, Llama 3.1 405B, Llama 3.1 70B, Llama 3.1 8B
- Llama 4 Maverick, Llama 4 Scout

### Google Models
- Gemini 2.5 Flash, Gemini 2.5 Pro, Gemini 2.0 Flash
- Gemini Flash 1.5, Gemini Pro 1.5, Gemma 3 27B, Gemma 3 12B

### Mistral Models
- Mistral Large, Mistral Medium 3, Mistral Small, Mistral Nemo
- Codestral, Pixtral Large

### DeepSeek Models
- DeepSeek R1, DeepSeek Chat, DeepSeek R1 Distill 70B, DeepSeek R1 Distill 32B

### Qwen Models
- Qwen3 235B, Qwen3 32B, Qwen 2.5 72B, QwQ 32B

### X.AI Models
- Grok 3, Grok 3 Mini, Grok 2

### Perplexity Models
- Sonar Pro, Sonar Reasoning, R1-1776

### Other Models
- Cohere Command R+, NVIDIA Nemotron 70B, Microsoft Phi-4
- Liquid LFM 40B, MiniMax-01

## Migration from Gemini

This app has been completely migrated from Google's Gemini API to Puter.js, providing:
- **No API key management required** - Zero configuration needed
- **100+ AI models available** - Access to OpenAI, Anthropic, Meta, Google, Mistral, DeepSeek, Qwen, X.AI, Perplexity, and more
- **User-pays model** - Each user covers their own AI usage costs
- **Automatic user authentication** - Handled seamlessly by Puter.js
- **Simplified deployment** - No backend needed, pure frontend application
- **Model selection** - Choose the best AI model for your storytelling needs
